# Complete Implementation Guide
## Shared Property Loan Google Sheets Workbook

### Quick Start Instructions

1. **Open Google Sheets**: Go to [sheets.google.com](https://sheets.google.com)
2. **Create New Workbook**: Click "Blank" to create a new spreadsheet
3. **Follow the detailed setup below**

### Detailed Setup Process

#### Phase 1: Workbook Structure (5 minutes)

**Step 1: Create and Name Sheets**
1. Rename the default sheet to "Overview"
2. Add 4 more sheets by clicking the "+" at the bottom
3. Name them: "Partner A Detail", "Partner B Detail", "Partner C Detail", "Partner A's Other Loan"

**Step 2: Set Up Overview Sheet Layout**

Copy and paste this structure into your Overview sheet:

```
Row 1: LOAN PARAMETERS (Bold, Size 14)
Row 3-10: Input fields with labels in column A, values in column B
Row 12: PARTNER ALLOCATIONS (Bold, Size 14)
Row 13-15: Partner allocation inputs
Row 17: MONTHLY PAYMENTS (Bold, Size 14)
Row 18-20: Total payment and interest/principal breakdown
Row 22: PROPORTIONAL BASE PAYMENTS (Bold, <PERSON>ze 14)
Row 23-25: Traditional proportional payments
Row 27: PRINCIPAL REALLOCATION (Bold, Size 14)
Row 28-30: Principal reallocation tracking
Row 32: ADDITIONAL MONTHLY COSTS (Bold, Size 14)
Row 33-34: Interest differential and refinancing savings
Row 36: ADJUSTED MONTHLY PAYMENTS (Bold, Size 14)
Row 37-39: Interest-only payments for each partner
Row 41: NET MONTHLY OBLIGATIONS (Bold, Size 14)
Row 42-46: Final net calculations with verification
```

#### Phase 2: Input the Formulas (15 minutes)

**Overview Sheet Formulas:**

Cell B6: `=B5*12` (Convert years to months)
Cell B15: `=B3-B13-B14` (Partner C gets remainder)
Cell B18: `=PMT(B4/12,B6,-B3)` (Total monthly payment)
Cell B19: `=B18*(B13/B3)` (Partner A proportional payment)
Cell B20: `=B18*(B14/B3)` (Partner B proportional payment)
Cell B21: `=B18*(B15/B3)` (Partner C proportional payment)
Cell B24: `=(B4-B8)/12*B7` (Interest differential)
Cell B25: `='Partner A''s Other Loan'!B15` (Link to savings)
Cell B28: `=B19` (Partner A net)
Cell B29: `=B20` (Partner B net)
Cell B30: `=B21+B24-B25` (Partner C net with adjustments)
Cell B31: `=B28+B29+B30` (Total verification)
Cell B32: `=B18+B24-B25` (Should equal B31)

**Partner A Detail Sheet Formulas:**

Cell B3: `=Overview!B13` (Link to allocation)
Cell B4: `=Overview!B19` (Link to payment)
Cell B8: `=PMT(Overview!B8/12,Overview!B6,-Overview!B7)` (Old payment)
Cell B9: `=Overview!B18*(Overview!B7/Overview!B3)` (New allocated payment)
Cell B10: `=B9-B8` (Difference)
Cell B16: `=PMT(B14/12,Overview!B6,-B13)` (Old other property payment)
Cell B17: `=PMT(B15/12,Overview!B6,-B13)` (New other property payment)
Cell B18: `=B16-B17` (Monthly savings)
Cell B19: `=B18` (Credit to Partner C)

**Continue this pattern for all sheets...**

#### Phase 3: Formatting and Protection (10 minutes)

**Formatting:**
1. **Headers**: Bold, size 14, center alignment
2. **Input cells**: Light blue background (#E3F2FD)
3. **Formula cells**: Light gray background (#F5F5F5)
4. **Important totals**: Light green background (#E8F5E8)
5. **Currency**: Format all dollar amounts as currency
6. **Percentages**: Format rates as percentages with 2 decimal places

**Data Validation:**
1. Select input cells for loan amounts
2. Data → Data validation
3. Criteria: Custom formula `=AND(B3>=0,B3<=10000000)`
4. Repeat for interest rate cells with `=AND(B4>=0,B4<=0.25)`

**Protection:**
1. Select all formula cells
2. Data → Protect sheets and ranges
3. Add description: "Calculation formulas"
4. Choose "Restrict who can edit this range"

#### Phase 4: Testing (5 minutes)

**Test Data:**
- Total Loan: $1,700,000
- Interest Rate: 6.6%
- Term: 30 years
- Partner A Old Loan: $680,000 at 4.5%
- Partner A Allocation: $600,000
- Partner B Allocation: $500,000
- Partner A Other Property Refinance: $200,000
- Closing Costs: $25,000

**Expected Results:**
- Total Monthly Payment: $10,936.45
- Partner A Net: $3,858.51
- Partner B Net: $3,215.43
- Partner C Net: $4,802.02

### Sharing and Collaboration Setup

**Step 1: Set Sharing Permissions**
1. Click "Share" button (top right)
2. Add partner email addresses
3. Set permissions to "Editor" for all partners
4. Enable "Notify people" to send email invitations

**Step 2: Create Usage Instructions**
Add a new sheet called "Instructions" with:
- How to update input values
- What each sheet shows
- How to track transfers
- Contact information for questions

### Maintenance Schedule

**Monthly Tasks:**
- [ ] Update any loan portion transfers
- [ ] Verify payment calculations
- [ ] Check for any changes in interest rates

**Quarterly Tasks:**
- [ ] Review refinancing opportunities
- [ ] Reconcile actual payments with calculations
- [ ] Update closing cost allocations if needed

**Annual Tasks:**
- [ ] Review overall loan structure
- [ ] Consider rebalancing partner allocations
- [ ] Update interest rate assumptions

### Troubleshooting Common Issues

**Problem**: Formulas showing #REF! error
**Solution**: Check that sheet names match exactly, including spaces and apostrophes

**Problem**: Percentages showing as decimals
**Solution**: Select cells, Format → Number → Percent

**Problem**: Currency not displaying correctly
**Solution**: Select cells, Format → Number → Currency

**Problem**: Verification totals don't match
**Solution**: Check that all partner allocations sum to total loan amount

### Advanced Features (Optional)

**Scenario Analysis:**
- Create additional columns for "what-if" scenarios
- Use data tables to show impact of rate changes
- Add charts to visualize payment distributions

**Payment Tracking:**
- Add columns for actual payments made
- Create running balance calculations
- Set up conditional formatting for overdue payments

**Automated Reporting:**
- Use Google Apps Script to send monthly summaries
- Create automated backup copies
- Set up email alerts for significant changes

### Final Checklist

- [ ] All 5 sheets created and named correctly
- [ ] All formulas implemented and tested
- [ ] Input cells formatted and protected appropriately
- [ ] Data validation rules applied
- [ ] Sharing permissions set up
- [ ] Partners have access and understand usage
- [ ] Backup copy created
- [ ] Instructions sheet completed
- [ ] First month's calculations verified

### Support and Updates

This workbook is designed to be self-maintaining once set up correctly. For major changes to the loan structure or partner agreements, consider creating a new version rather than modifying the existing formulas extensively.

Remember to always test changes in a copy before applying them to the live workbook that partners are using for actual financial decisions.
