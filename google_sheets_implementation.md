# Google Sheets Implementation Guide
## Shared Property Loan Workbook

### Sheet 1: Overview

**Row 1-15: Input Variables Section**
```
A1: "LOAN PARAMETERS"
A3: "Total New Loan Amount"          B3: $1,700,000
A4: "New Loan Interest Rate"         B4: 6.6%
A5: "Loan Term (Years)"              B5: 30
A6: "Loan Term (Months)"             B6: =B5*12
A7: "Partner A Old Loan Amount"      B7: $680,000
A8: "Partner A Old Interest Rate"    B8: 4.5%
A9: "Partner A Other Property Rate"  B9: 8.5%
A10: "Closing Costs"                 B10: [INPUT REQUIRED]

A12: "PARTNER ALLOCATIONS"
A13: "Partner A Loan Portion"        B13: [INPUT REQUIRED]
A14: "Partner B Loan Portion"        B14: [INPUT REQUIRED]
A15: "Partner C Loan Portion"        B15: =B3-B13-B14
```

**Row 17-30: Payment Calculations**
```
A17: "MONTHLY PAYMENTS"
A18: "Total Monthly Payment"         B18: =PMT(B4/12,B6,-B3)
A19: "Monthly Interest Only"         B19: =B3*B4/12
A20: "Monthly Principal Only"        B20: =B18-B19

A22: "PROPORTIONAL BASE PAYMENTS"
A23: "Partner A Base Payment"        B23: =B18*(B13/B3)
A24: "Partner B Base Payment"        B24: =B18*(B14/B3)
A25: "Partner C Base Payment"        B25: =B18*(B15/B3)

A27: "PRINCIPAL REALLOCATION"
A28: "Partner A Gets All Principal"  B28: =B20
A29: "Partner B Principal Portion"   B29: =B20*(B14/B3)
A30: "Partner C Principal Portion"   B30: =B20*(B15/B3)
```

**Row 32-45: Additional Costs and Net Obligations**
```
A32: "ADDITIONAL MONTHLY COSTS"
A33: "Interest Differential (C pays)" B33: =(B4-B8)/12*B7
A34: "A's Refinancing Savings"       B34: ='Partner A''s Other Loan'!B15

A36: "ADJUSTED MONTHLY PAYMENTS"
A37: "Partner A Interest Only"       B37: =B19*(B13/B3)
A38: "Partner B Interest Only"       B38: =B19*(B14/B3)
A39: "Partner C Interest Only"       B39: =B19*(B15/B3)

A41: "NET MONTHLY OBLIGATIONS"
A42: "Partner A Net Monthly"         B42: =B37+B28
A43: "Partner B Net Monthly"         B43: =B38
A44: "Partner C Net Monthly"         B44: =B39+B33-B34
A45: "Total Verification"            B45: =B42+B43+B44
A46: "Should Equal"                  B46: =B18+B33-B34
```

### Sheet 2: Partner A Detail

**Input Section (A1-B15)**
```
A1: "PARTNER A LOAN DETAILS"
A3: "Current Loan Portion"           B3: =Overview!B13
A4: "Proportional Base Payment"      B4: =Overview!B23
A5: "Interest Only Payment"          B5: =Overview!B37
A6: "All Principal Payments"         B6: =Overview!B28
A7: "Total Monthly Payment"          B7: =B5+B6
A8: "Other Property Refinance Amt"   B8: [INPUT REQUIRED]

A10: "OLD LOAN COMPARISON"
A11: "Old Monthly Payment (4.5%)"    B11: =PMT(Overview!B8/12,Overview!B6,-Overview!B7)
A12: "New Allocated Payment"         B12: =Overview!B18*(Overview!B7/Overview!B3)
A13: "Monthly Difference"            B13: =B12-B11

A15: "PRINCIPAL REALLOCATION IMPACT"
A16: "Principal from Partner B"      B16: =Overview!B29
A17: "Principal from Partner C"      B17: =Overview!B30
A18: "Total Additional Principal"    B18: =B16+B17
```

**Refinancing Section (A20-B28)**
```
A20: "OTHER PROPERTY REFINANCING"
A21: "Amount Being Refinanced"       B21: =B8
A22: "Old Rate (8.5%)"              B22: =Overview!B9
A23: "New Rate (6.6%)"              B23: =Overview!B4
A24: "Old Monthly Payment"           B24: =PMT(B22/12,Overview!B6,-B21)
A25: "New Monthly Payment"           B25: =PMT(B23/12,Overview!B6,-B21)
A26: "Monthly Savings"               B26: =B24-B25
A27: "Credit to Partner C"           B27: =B26

A29: "NET MONTHLY SUMMARY"
A30: "Base Interest Payment"         B30: =B5
A31: "All Principal Payments"        B31: =B6
A32: "Total Monthly Obligation"      B32: =B30+B31
```

### Sheet 3: Partner B Detail

**Loan Structure (A1-B20)**
```
A1: "PARTNER B LOAN DETAILS"
A3: "Current Loan Portion"           B3: =Overview!B14
A4: "Proportional Base Payment"      B4: =Overview!B24
A5: "Interest Only Payment"          B5: =Overview!B38
A6: "Principal Portion (Goes to A)"  B6: =Overview!B29
A7: "Net Monthly Payment"            B7: =B5

A9: "PAYMENT BREAKDOWN"
A10: "What B Would Pay (Full)"       B10: =B4
A11: "Principal Transferred to A"    B11: =B6
A12: "B's Actual Payment"            B12: =B10-B11
A13: "Verification (Should = B7)"    B13: =B12

A15: "TRANSFER TRACKING"
A16: "Original Allocation"           B16: =B3
A17: "Transfers Out"                 B17: [INPUT for tracking]
A18: "Transfers In"                  B18: [INPUT for tracking]
A19: "Current Responsibility"        B19: =B16-B17+B18
A20: "Updated Interest Payment"      B20: =Overview!B19*(B19/Overview!B3)
```

### Sheet 4: Partner C Detail

**Base Loan Section (A1-B15)**
```
A1: "PARTNER C LOAN DETAILS"
A3: "Current Loan Portion"           B3: =Overview!B15
A4: "Proportional Base Payment"      B4: =Overview!B25
A5: "Interest Only Payment"          B5: =Overview!B39
A6: "Principal Portion (Goes to A)"  B6: =Overview!B30
A7: "Net Monthly Payment"            B7: =B5

A9: "PAYMENT BREAKDOWN"
A10: "What C Would Pay (Full)"       B10: =B4
A11: "Principal Transferred to A"    B11: =B6
A12: "C's Base Payment"              B12: =B10-B11
A13: "Verification (Should = B7)"    B13: =B12

A15: "TRANSFER TRACKING"
A16: "Transfer Additions"            B16: [INPUT for tracking transfers]
A17: "Total Loan Responsibility"     B17: =B3+B16
A18: "Updated Interest Payment"      B18: =Overview!B19*(B17/Overview!B3)
```

**Additional Costs (A20-B30)**
```
A20: "ADDITIONAL RESPONSIBILITIES"
A21: "Closing Costs (One-time)"      B21: =Overview!B10
A22: "Interest Differential"         B22: =Overview!B33
A23: "Monthly Differential Cost"     B23: =B22

A25: "CREDITS RECEIVED"
A26: "A's Refinancing Savings"       B26: ='Partner A Detail'!B27
A27: "Other Credits"                 B27: [INPUT field]
A28: "Total Monthly Credits"         B28: =B26+B27

A30: "NET MONTHLY OBLIGATION"
A31: "Base Interest Payment"         B31: =B7
A32: "Interest Differential"         B32: =B23
A33: "Less: Credits Received"        B33: =B28
A34: "Total Monthly Payment"         B34: =B31+B32-B33
```

### Sheet 5: Partner A's Other Loan

**Loan Details (A1-B15)**
```
A1: "OTHER PROPERTY LOAN DETAILS"
A3: "Refinanced Amount"              B3: ='Partner A Detail'!B5
A4: "Original Interest Rate"         B4: 8.5%
A5: "New Interest Rate"              B5: 6.6%
A6: "Loan Term (Months)"            B6: =Overview!B6

A8: "PAYMENT COMPARISON"
A9: "Original Monthly Payment"       B9: =PMT(B4/12,B6,-B3)
A10: "New Monthly Payment"           B10: =PMT(B5/12,B6,-B3)
A11: "Monthly Savings"               B11: =B9-B10
A12: "Annual Savings"                B12: =B11*12

A14: "CREDIT TO PARTNER C"
A15: "Monthly Credit Amount"         B15: =B11
```

## Implementation Steps

### Step 1: Create the Workbook Structure
1. Go to [sheets.google.com](https://sheets.google.com)
2. Create a new blank spreadsheet
3. Rename it to "Shared Property Loan - Partners A, B, C"
4. Create 5 sheets by right-clicking on sheet tabs:
   - Sheet 1: "Overview"
   - Sheet 2: "Partner A Detail"
   - Sheet 3: "Partner B Detail"
   - Sheet 4: "Partner C Detail"
   - Sheet 5: "Partner A's Other Loan"

### Step 2: Set Up the Overview Sheet
1. **Format headers**: Make row 1, 12, 17, 23, 27 bold and larger font
2. **Add borders**: Around each section for clarity
3. **Color coding**:
   - Input cells (light blue background)
   - Calculation cells (light gray background)
   - Important totals (light green background)

### Step 3: Implement Data Validation
**For input cells, add data validation:**
- Loan amounts: Custom formula `=AND(VALUE>=0, VALUE<=10000000)`
- Interest rates: Custom formula `=AND(VALUE>=0, VALUE<=0.2)`
- Terms: Custom formula `=AND(VALUE>=1, VALUE<=50)`

### Step 4: Add Conditional Formatting
- Negative values: Red text
- Values over $5,000: Bold formatting
- Verification cells: Green if match, red if don't match

### Step 5: Protect Formulas
1. Select all formula cells
2. Right-click → "Protect range"
3. Add description: "Formula cells - do not edit"
4. Set permissions to "Restrict who can edit this range"

### Step 6: Test with Sample Data
Use the sample calculations provided to verify all formulas work correctly

## Key Features Implemented

- ✅ Monthly payment calculations for all partners
- ✅ Interest differential tracking for Partner C
- ✅ Refinancing savings calculation and credit system
- ✅ Transfer tracking capability
- ✅ Verification formulas to ensure accuracy
- ✅ Clear input/output separation
- ✅ Cross-sheet references for data consistency

## Usage Instructions

1. **Fill in all INPUT REQUIRED fields** in the Overview sheet
2. **Enter Partner A's other property refinance amount** in Partner A Detail sheet
3. **Track any transfers** between partners in respective detail sheets
4. **Monitor net monthly obligations** in Overview sheet
5. **Verify totals** using the verification formulas provided
